{"name": "has-tostringtag", "version": "1.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "description": "Determine if the JS environment has `Symbol.toStringTag` support. Supports spec, or shams.", "license": "MIT", "main": "index.js", "types": "./index.d.ts", "exports": {".": [{"types": "./index.d.ts", "default": "./index.js"}, "./index.js"], "./shams": [{"types": "./shams.d.ts", "default": "./shams.js"}, "./shams.js"], "./package.json": "./package.json"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run --silent lint", "test": "npm run tests-only", "posttest": "aud --production", "tests-only": "npm run test:stock && npm run test:shams", "test:stock": "nyc node test", "test:staging": "nyc node --harmony --es-staging test", "test:shams": "npm run --silent test:shams:getownpropertysymbols && npm run --silent test:shams:corejs", "test:shams:corejs": "nyc node test/shams/core-js.js", "test:shams:getownpropertysymbols": "nyc node test/shams/get-own-property-symbols.js", "lint": "eslint --ext=js,mjs .", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/has-tostringtag.git"}, "bugs": {"url": "https://github.com/inspect-js/has-tostringtag/issues"}, "homepage": "https://github.com/inspect-js/has-tostringtag#readme", "keywords": ["javascript", "ecmascript", "symbol", "symbols", "tostringtag", "Symbol.toStringTag"], "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "@types/has-symbols": "^1.0.2", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "core-js": "^2.6.12", "eslint": "=8.8.0", "get-own-property-symbols": "^0.9.5", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.4", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "dependencies": {"has-symbols": "^1.0.3"}}